#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Отладочный скрипт для проверки работы улучшенного AI индикатора
"""

import pandas as pd
import numpy as np
from ai_indicator_enhanced import EnhancedAITrendReversalIndicator

def debug_indicator():
    """Отладка работы индикатора"""
    print("🔍 Отладка улучшенного AI индикатора...")
    
    # Загрузка данных
    try:
        df = pd.read_csv("OKX_BTCUSDT.P, 5_d4984.csv")
        print(f"📊 Загружено {len(df)} строк данных")
        
        # Переименование колонок
        if 'time' in df.columns:
            df = df.rename(columns={'time': 'timestamp'})
        if 'Volume' in df.columns:
            df = df.rename(columns={'Volume': 'volume'})
            
        # Выбираем только нужные колонки
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        df = df[required_columns + ['timestamp']].copy()
        
        # Конвертация типов
        for col in required_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            
        # Очистка данных
        df = df.dropna(subset=required_columns)
        df = df[df['volume'] > 0]
        
        print(f"✅ После очистки: {len(df)} баров")
        
        if len(df) < 100:
            print("❌ Недостаточно данных для тестирования")
            return
            
        # Создание индикатора с мягкими настройками
        config = {
            'ai_confidence_threshold': 30.0,  # Снижаем пороги
            'min_signal_strength': 20,
            'probability_threshold': 30.0,
            'ml_score_threshold': 0.3,
            'enable_ai_learning': True,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'rsi_period': 14,
            'enable_market_regime_filter': False,  # Отключаем фильтры
            'enable_volatility_filter': False,
            'enable_momentum_filter': False,
            'volume_confirmation_required': False,
        }
        
        indicator = EnhancedAITrendReversalIndicator(config)
        
        print("🚀 Начинаем обработку данных...")
        
        signals = []
        processed_bars = 0
        
        # Обрабатываем данные по барам
        for i in range(len(df)):
            if i < 50:  # Пропускаем первые 50 баров для накопления истории
                continue
                
            row = df.iloc[i]
            
            # Создаем бар данных с историей
            bar_data = {
                'open': df['open'].iloc[:i+1].tolist(),
                'high': df['high'].iloc[:i+1].tolist(),
                'low': df['low'].iloc[:i+1].tolist(),
                'close': df['close'].iloc[:i+1].tolist()
            }
            
            volume_data = df['volume'].iloc[:i+1].tolist()
            
            try:
                # Обрабатываем бар
                signal = indicator.process_bar_enhanced(bar_data, volume_data, i)
                processed_bars += 1
                
                if signal and signal.signal_type != "none":
                    signals.append({
                        'bar_index': i,
                        'signal_type': signal.signal_type,
                        'probability': signal.probability,
                        'confidence': signal.confidence,
                        'strength': signal.strength,
                        'ml_score': getattr(signal, 'ml_score', 0),
                        'reasoning': getattr(signal, 'reasoning', 'No reasoning provided')
                    })
                    print(f"📈 Сигнал #{len(signals)}: {signal.signal_type} на баре {i}")
                    print(f"   Вероятность: {signal.probability:.1f}%, Уверенность: {signal.confidence:.1f}%")
                    
                # Выводим прогресс каждые 500 баров
                if (i + 1) % 500 == 0:
                    print(f"⏳ Обработано {i + 1}/{len(df)} баров, найдено {len(signals)} сигналов")
                    
            except Exception as e:
                print(f"❌ Ошибка обработки бара {i}: {e}")
                continue
                
        print(f"\n✅ Обработка завершена!")
        print(f"📊 Обработано баров: {processed_bars}")
        print(f"🎯 Найдено сигналов: {len(signals)}")
        
        if signals:
            print(f"\n📈 Первые 5 сигналов:")
            for i, signal in enumerate(signals[:5]):
                print(f"  {i+1}. Бар {signal['bar_index']}: {signal['signal_type']}")
                print(f"     Вероятность: {signal['probability']:.1f}%, Уверенность: {signal['confidence']:.1f}%")
                print(f"     ML Score: {signal['ml_score']:.3f}")
                print(f"     Причина: {signal['reasoning'][:100]}...")
                print()
        else:
            print("❌ Сигналы не найдены. Возможные причины:")
            print("   - Слишком строгие пороги")
            print("   - Недостаточно данных для ML модели")
            print("   - Ошибки в логике индикатора")
            
            # Проверим статистику индикатора
            stats = indicator.get_performance_stats()
            print(f"\n📊 Статистика индикатора:")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_indicator()