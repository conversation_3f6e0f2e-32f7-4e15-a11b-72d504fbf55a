#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестирование AI индикатора на реальных данных OKX BTCUSDT
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json
from ai_indicator_full import AITrendReversalIndicator
from test_scenarios import IndicatorTester

class RealDataTester:
    """Тестирование AI индикатора на реальных рыночных данных"""
    
    def __init__(self):
        self.test_scenarios = IndicatorTester()
        self.results = []
    
    def load_csv_data(self, filename: str) -> dict:
        """Загрузка данных из CSV файла OKX"""
        print(f"📊 Загрузка данных из {filename}...")
        
        try:
            # Читаем CSV файл
            df = pd.read_csv(filename, header=None)
            
            # Определяем колонки на основе структуры OKX данных
            # timestamp, open, high, low, close, volume, ...
            df.columns = [
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'col6', 'col7', 'col8', 'col9', 'col10', 'col11', 'col12', 'col13',
                'col14', 'col15', 'col16', 'col17', 'col18', 'col19'
            ]
            
            # Конвертируем в числовые значения, заменяя NaN на 0
            df['open'] = pd.to_numeric(df['open'], errors='coerce').fillna(0)
            df['high'] = pd.to_numeric(df['high'], errors='coerce').fillna(0)
            df['low'] = pd.to_numeric(df['low'], errors='coerce').fillna(0)
            df['close'] = pd.to_numeric(df['close'], errors='coerce').fillna(0)
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0)
            df['timestamp'] = pd.to_numeric(df['timestamp'], errors='coerce').fillna(0)
            
            # Удаляем строки с нулевыми ценами
            df = df[(df['open'] > 0) & (df['high'] > 0) & (df['low'] > 0) & (df['close'] > 0)]
            
            # Берем только нужные колонки OHLCV
            ohlcv_data = {
                'open': df['open'].tolist(),
                'high': df['high'].tolist(), 
                'low': df['low'].tolist(),
                'close': df['close'].tolist(),
                'volume': df['volume'].tolist(),
                'timestamp': df['timestamp'].tolist()
            }
            
            print(f"✅ Загружено {len(df)} баров данных")
            print(f"📈 Диапазон цен: {df['low'].min():.2f} - {df['high'].max():.2f}")
            print(f"📅 Временной период: {len(df)} баров")
            
            return ohlcv_data
            
        except Exception as e:
            print(f"❌ Ошибка загрузки данных: {e}")
            return None
    
    def test_with_preset(self, data: dict, preset_name: str) -> dict:
        """Тестирование с конкретным пресетом"""
        print(f"\n🧪 Тестирование с пресетом: {preset_name}")
        
        try:
            # Инициализация индикатора
            ai_indicator = self.test_scenarios.initialize_ai_indicator(preset_name)
            
            # Результаты тестирования
            results = {
                'preset': preset_name,
                'data_source': 'OKX_BTCUSDT_real',
                'bars_count': len(data['close']),
                'signals': [],
                'statistics': {},
                'timestamp': datetime.now().isoformat()
            }
            
            # Обработка каждого бара
            valid_signals = 0
            total_signals = 0
            
            # Подготавливаем данные для индикатора (нужны массивы, а не отдельные значения)
            min_bars = 50  # Минимум баров для анализа
            
            for i in range(min_bars, len(data['close'])):
                # Берем последние min_bars + текущий бар для анализа
                start_idx = max(0, i - min_bars + 1)
                end_idx = i + 1
                
                ohlc_data = {
                    'open': data['open'][start_idx:end_idx],
                    'high': data['high'][start_idx:end_idx],
                    'low': data['low'][start_idx:end_idx],
                    'close': data['close'][start_idx:end_idx]
                }
                
                volume_data = data['volume'][start_idx:end_idx]
                
                # Обработка бара индикатором
                signal = ai_indicator.process_bar(ohlc_data, volume_data, i)
                
                if signal:
                    total_signals += 1
                    
                    # Проверка валидности сигнала (упрощенная)
                    is_valid = self.validate_signal(signal, data, i)
                    if is_valid:
                        valid_signals += 1
                    
                    signal_data = {
                        'bar_index': i,
                        'timestamp': data['timestamp'][i] if 'timestamp' in data else i,
                        'signal_type': signal.signal_type,
                        'probability': signal.probability,
                        'confidence': signal.confidence,
                        'strength': signal.strength,
                        'math_expectation': signal.math_expectation,
                        'risk_score': signal.risk_score,
                        'reasoning': signal.reasoning,
                        'is_valid': is_valid,
                        'price': data['close'][i]
                    }
                    
                    results['signals'].append(signal_data)
            
            # Статистика
            accuracy = (valid_signals / total_signals * 100) if total_signals > 0 else 0
            results['statistics'] = {
                'total_signals': total_signals,
                'valid_signals': valid_signals,
                'accuracy': accuracy,
                'signal_frequency': total_signals / len(data['close']) * 100
            }
            
            print(f"📊 Результаты:")
            print(f"   • Всего сигналов: {total_signals}")
            print(f"   • Валидных сигналов: {valid_signals}")
            print(f"   • Точность: {accuracy:.1f}%")
            print(f"   • Частота сигналов: {results['statistics']['signal_frequency']:.2f}%")
            
            return results
            
        except Exception as e:
            print(f"❌ Ошибка тестирования: {e}")
            return {'error': str(e)}
    
    def validate_signal(self, signal: dict, data: dict, current_index: int) -> bool:
        """Упрощенная валидация сигнала на реальных данных"""
        if current_index >= len(data['close']) - 5:  # Недостаточно данных для проверки
            return False
        
        signal_type = signal.signal_type
        current_price = data['close'][current_index]
        
        # Проверяем движение цены в следующие 3-5 баров
        future_prices = data['close'][current_index + 1:current_index + 6]
        
        if signal_type == 'bullish_reversal':
            # Ожидаем рост цены
            max_future_price = max(future_prices)
            return max_future_price > current_price * 1.005  # Рост минимум на 0.5%
        
        elif signal_type == 'bearish_reversal':
            # Ожидаем падение цены
            min_future_price = min(future_prices)
            return min_future_price < current_price * 0.995  # Падение минимум на 0.5%
        
        return False
    
    def run_comprehensive_test(self, csv_filename: str):
        """Запуск комплексного тестирования на реальных данных"""
        print("🚀 Запуск тестирования AI индикатора на реальных данных OKX BTCUSDT")
        print("=" * 70)
        
        # Загрузка данных
        data = self.load_csv_data(csv_filename)
        if not data:
            return
        
        # Список пресетов для тестирования
        presets = ['conservative', 'aggressive', 'crypto', 'scalping', 'crypto_swing']
        
        all_results = []
        
        for preset in presets:
            result = self.test_with_preset(data, preset)
            if 'error' not in result:
                all_results.append(result)
                self.results.append(result)
        
        # Сводная статистика
        self.print_summary(all_results)
        
        # Сохранение результатов
        self.save_results(all_results, 'real_data_test_results.json')
        
        return all_results
    
    def print_summary(self, results: list):
        """Вывод сводной статистики"""
        print("\n" + "=" * 70)
        print("📊 СВОДНАЯ СТАТИСТИКА ПО РЕАЛЬНЫМ ДАННЫМ")
        print("=" * 70)
        
        if not results:
            print("❌ Нет результатов для анализа")
            return
        
        total_signals = sum(r['statistics']['total_signals'] for r in results)
        total_valid = sum(r['statistics']['valid_signals'] for r in results)
        overall_accuracy = (total_valid / total_signals * 100) if total_signals > 0 else 0
        
        print(f"\n🎯 Общие результаты:")
        print(f"   • Всего сигналов: {total_signals}")
        print(f"   • Валидных сигналов: {total_valid}")
        print(f"   • Общая точность: {overall_accuracy:.1f}%")
        
        print(f"\n📋 Детальные результаты по пресетам:")
        for i, result in enumerate(results, 1):
            stats = result['statistics']
            print(f"   {i}. {result['preset']}: {stats['total_signals']} сигналов, "
                  f"{stats['valid_signals']} валидных, точность {stats['accuracy']:.1f}%")
        
        # Лучший пресет
        best_preset = max(results, key=lambda x: x['statistics']['accuracy'])
        print(f"\n🏆 Лучший пресет: {best_preset['preset']} "
              f"(точность {best_preset['statistics']['accuracy']:.1f}%)")
    
    def save_results(self, results: list, filename: str):
        """Сохранение результатов в JSON файл"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            print(f"\n💾 Результаты сохранены в файл: {filename}")
        except Exception as e:
            print(f"\n❌ Ошибка сохранения: {e}")

def main():
    """Главная функция"""
    tester = RealDataTester()
    
    # Запуск тестирования
    csv_file = "OKX_BTCUSDT.P, 5_d4984.csv"
    results = tester.run_comprehensive_test(csv_file)
    
    print("\n✅ Тестирование завершено!")

if __name__ == "__main__":
    main()