"""
Полная реализация AI Trend Reversal Indicator
Максимальное соответствие оригинальному PineScript индикатору
"""

import numpy as np
import pandas as pd
import json
import math
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

@dataclass
class AISignal:
    """Структура AI сигнала"""
    signal_type: str  # "bullish_reversal", "bearish_reversal", "none"
    probability: float  # Вероятность в процентах
    confidence: float  # Уверенность AI в процентах
    strength: int  # Сила сигнала 0-100
    math_expectation: float  # Математическое ожидание
    risk_score: float  # Оценка риска
    reasoning: str  # Обоснование сигнала
    is_valid: bool  # Валидность сигнала
    timestamp: int  # Временная метка

@dataclass
class BayesianModel:
    """Байесовская модель для AI"""
    prior_bullish: float = 0.5
    prior_bearish: float = 0.5
    model_accuracy: float = 0.5
    training_samples: int = 0
    successful_predictions: int = 0
    total_predictions: int = 0
    adaptation_rate: float = 0.1

@dataclass
class SRLevel:
    """Уровень поддержки/сопротивления"""
    price: float
    touches: int
    strength: float
    is_support: bool
    last_touch_bar: int
    creation_bar_index: int

class AITrendReversalIndicator:
    """Полная реализация AI Trend Reversal Indicator"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Инициализация индикатора"""
        # Объединяем базовую конфигурацию с переданной
        self.config = self._default_config()
        if config:
            self.config.update(config)
        
        # AI и машинное обучение
        self.bayesian_model = BayesianModel()
        self.ai_signals_history = []
        self.ml_features_history = []
        
        # Технические индикаторы
        self.macd_history = []
        self.rsi_history = []
        self.sma_history = []
        self.atr_history = []
        
        # Дивергенции
        self.macd_divergences = []
        self.rsi_divergences = []
        
        # Свечные паттерны
        self.candle_patterns = []
        
        # Уровни поддержки/сопротивления
        self.support_levels = []
        self.resistance_levels = []
        
        # Объемный анализ
        self.volume_history = []
        self.volume_spikes = []
        
        # Multi-timeframe данные
        self.mtf_data = {}
        
        # Статистика производительности
        self.performance_stats = {
            'total_signals': 0,
            'successful_signals': 0,
            'win_rate': 0.0,
            'avg_return': 0.0
        }
        
    def _default_config(self) -> Dict[str, Any]:
        """Конфигурация по умолчанию"""
        return {
            # AI настройки
            'ai_confidence_threshold': 75.0,
            'min_signal_strength': 65,
            'probability_model': 'Advanced',
            'enable_ai_learning': True,
            
            # Технические индикаторы
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'rsi_period': 14,
            'sma_period': 20,
            'atr_period': 14,
            
            # Multi-timeframe
            'enable_mtf': True,
            'mtf_confluence_required': 2,
            'mtf_timeframes': ['5m', '15m', '1h'],
            
            # Объемный анализ
            'enable_volume_analysis': True,
            'volume_spike_threshold': 1.5,
            'volume_ma_period': 20,
            
            # Поддержка/сопротивление
            'enable_sr_analysis': True,
            'sr_lookback': 50,
            'sr_min_touches': 2,
            
            # Риск-менеджмент
            'stop_loss_atr_multiplier': 2.0,
            'take_profit_atr_multiplier': 4.0,
            'risk_reward_ratio': 2.0,
            
            # Фильтры качества
            'quality_filter': True,
            'adaptive_thresholds': True,
            'min_bars_between_signals': 5
        }
    
    def calculate_macd(self, prices: List[float]) -> Tuple[float, float, float]:
        """Расчет MACD"""
        if len(prices) < self.config['macd_slow']:
            return 0.0, 0.0, 0.0
            
        prices_array = np.array(prices)
        
        # EMA расчет
        def ema(data, period):
            alpha = 2 / (period + 1)
            ema_values = [data[0]]
            for i in range(1, len(data)):
                ema_values.append(alpha * data[i] + (1 - alpha) * ema_values[-1])
            return ema_values[-1]
        
        ema_fast = ema(prices_array, self.config['macd_fast'])
        ema_slow = ema(prices_array, self.config['macd_slow'])
        macd_line = ema_fast - ema_slow
        
        # Signal line
        if len(self.macd_history) >= self.config['macd_signal']:
            macd_values = [h['macd'] for h in self.macd_history[-self.config['macd_signal']:]]
            macd_values.append(macd_line)
            signal_line = ema(macd_values, self.config['macd_signal'])
        else:
            signal_line = macd_line
            
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def calculate_rsi(self, prices: List[float]) -> float:
        """Расчет RSI"""
        if len(prices) < self.config['rsi_period'] + 1:
            return 50.0
            
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-self.config['rsi_period']:])
        avg_loss = np.mean(losses[-self.config['rsi_period']:])
        
        if avg_loss == 0:
            return 100.0
            
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def calculate_atr(self, highs: List[float], lows: List[float], closes: List[float]) -> float:
        """Расчет ATR"""
        if len(closes) < 2:
            return 0.0
            
        true_ranges = []
        for i in range(1, len(closes)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - closes[i-1])
            tr3 = abs(lows[i] - closes[i-1])
            true_ranges.append(max(tr1, tr2, tr3))
        
        if len(true_ranges) >= self.config['atr_period']:
            return np.mean(true_ranges[-self.config['atr_period']:])
        else:
            return np.mean(true_ranges) if true_ranges else 0.0
    
    def detect_macd_divergence(self, prices: List[float], macd_values: List[float]) -> str:
        """Обнаружение дивергенций MACD"""
        if len(prices) < 10 or len(macd_values) < 10:
            return "none"
        
        # Поиск локальных максимумов и минимумов
        price_peaks = self._find_peaks(prices[-20:])
        macd_peaks = self._find_peaks(macd_values[-20:])
        
        if len(price_peaks) >= 2 and len(macd_peaks) >= 2:
            # Бычья дивергенция: цена делает новые минимумы, MACD - нет
            if (prices[price_peaks[-1]] < prices[price_peaks[-2]] and 
                macd_values[macd_peaks[-1]] > macd_values[macd_peaks[-2]]):
                return "bullish"
            
            # Медвежья дивергенция: цена делает новые максимумы, MACD - нет
            if (prices[price_peaks[-1]] > prices[price_peaks[-2]] and 
                macd_values[macd_peaks[-1]] < macd_values[macd_peaks[-2]]):
                return "bearish"
        
        return "none"
    
    def detect_rsi_divergence(self, prices: List[float], rsi_values: List[float]) -> str:
        """Обнаружение дивергенций RSI"""
        if len(prices) < 10 or len(rsi_values) < 10:
            return "none"
        
        price_peaks = self._find_peaks(prices[-20:])
        rsi_peaks = self._find_peaks(rsi_values[-20:])
        
        if len(price_peaks) >= 2 and len(rsi_peaks) >= 2:
            # Бычья дивергенция
            if (prices[price_peaks[-1]] < prices[price_peaks[-2]] and 
                rsi_values[rsi_peaks[-1]] > rsi_values[rsi_peaks[-2]]):
                return "bullish"
            
            # Медвежья дивергенция
            if (prices[price_peaks[-1]] > prices[price_peaks[-2]] and 
                rsi_values[rsi_peaks[-1]] < rsi_values[rsi_peaks[-2]]):
                return "bearish"
        
        return "none"
    
    def _find_peaks(self, data: List[float]) -> List[int]:
        """Поиск локальных экстремумов"""
        peaks = []
        for i in range(1, len(data) - 1):
            if data[i] > data[i-1] and data[i] > data[i+1]:
                peaks.append(i)
            elif data[i] < data[i-1] and data[i] < data[i+1]:
                peaks.append(i)
        return peaks
    
    def detect_candle_patterns(self, ohlc: Dict[str, List[float]]) -> List[str]:
        """Обнаружение свечных паттернов"""
        if len(ohlc['open']) < 3:
            return []
        
        patterns = []
        o, h, l, c = ohlc['open'][-1], ohlc['high'][-1], ohlc['low'][-1], ohlc['close'][-1]
        prev_o, prev_h, prev_l, prev_c = ohlc['open'][-2], ohlc['high'][-2], ohlc['low'][-2], ohlc['close'][-2]
        
        body = abs(c - o)
        prev_body = abs(prev_c - prev_o)
        upper_shadow = h - max(o, c)
        lower_shadow = min(o, c) - l
        
        # Doji
        if body < (h - l) * 0.1:
            patterns.append("doji")
        
        # Hammer
        if (lower_shadow > body * 2 and upper_shadow < body * 0.5 and 
            c > o and l < prev_l):
            patterns.append("hammer")
        
        # Shooting Star
        if (upper_shadow > body * 2 and lower_shadow < body * 0.5 and 
            c < o and h > prev_h):
            patterns.append("shooting_star")
        
        # Bullish Engulfing
        if (c > o and prev_c < prev_o and 
            c > prev_o and o < prev_c and body > prev_body):
            patterns.append("bullish_engulfing")
        
        # Bearish Engulfing
        if (c < o and prev_c > prev_o and 
            c < prev_o and o > prev_c and body > prev_body):
            patterns.append("bearish_engulfing")
        
        return patterns
    
    def update_support_resistance(self, ohlc: Dict[str, List[float]], bar_index: int):
        """Обновление уровней поддержки и сопротивления"""
        if len(ohlc['high']) < self.config['sr_lookback']:
            return
        
        # Поиск pivot points
        highs = ohlc['high'][-self.config['sr_lookback']:]
        lows = ohlc['low'][-self.config['sr_lookback']:]
        
        # Поиск локальных максимумов (сопротивления)
        for i in range(2, len(highs) - 2):
            if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and 
                highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                self._add_resistance_level(highs[i], bar_index - (len(highs) - i))
        
        # Поиск локальных минимумов (поддержки)
        for i in range(2, len(lows) - 2):
            if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and 
                lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                self._add_support_level(lows[i], bar_index - (len(lows) - i))
    
    def _add_support_level(self, price: float, bar_index: int):
        """Добавление уровня поддержки"""
        # Проверка на существующие близкие уровни
        tolerance = price * 0.001  # 0.1% толерантность
        
        for level in self.support_levels:
            if abs(level.price - price) < tolerance:
                level.touches += 1
                level.strength = min(100, level.strength + 10)
                level.last_touch_bar = bar_index
                return
        
        # Добавление нового уровня
        new_level = SRLevel(
            price=price,
            touches=1,
            strength=50.0,
            is_support=True,
            last_touch_bar=bar_index,
            creation_bar_index=bar_index
        )
        
        self.support_levels.append(new_level)
        
        # Ограничение количества уровней
        if len(self.support_levels) > 10:
            self.support_levels = sorted(self.support_levels, key=lambda x: x.strength, reverse=True)[:10]
    
    def _add_resistance_level(self, price: float, bar_index: int):
        """Добавление уровня сопротивления"""
        tolerance = price * 0.001
        
        for level in self.resistance_levels:
            if abs(level.price - price) < tolerance:
                level.touches += 1
                level.strength = min(100, level.strength + 10)
                level.last_touch_bar = bar_index
                return
        
        new_level = SRLevel(
            price=price,
            touches=1,
            strength=50.0,
            is_support=False,
            last_touch_bar=bar_index,
            creation_bar_index=bar_index
        )
        
        self.resistance_levels.append(new_level)
        
        if len(self.resistance_levels) > 10:
            self.resistance_levels = sorted(self.resistance_levels, key=lambda x: x.strength, reverse=True)[:10]
    
    def analyze_volume(self, volumes: List[float]) -> Dict[str, Any]:
        """Анализ объема"""
        if len(volumes) < self.config['volume_ma_period']:
            return {'spike': False, 'strength': 0, 'trend': 'neutral'}
        
        current_volume = volumes[-1]
        avg_volume = np.mean(volumes[-self.config['volume_ma_period']:])
        
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        return {
            'spike': volume_ratio >= self.config['volume_spike_threshold'],
            'strength': min(100, volume_ratio * 50),
            'trend': 'increasing' if volume_ratio > 1.2 else 'decreasing' if volume_ratio < 0.8 else 'neutral',
            'ratio': volume_ratio
        }
    
    def update_bayesian_model(self, signal_type: str, actual_outcome: bool):
        """Обновление байесовской модели"""
        self.bayesian_model.total_predictions += 1
        
        if actual_outcome:
            self.bayesian_model.successful_predictions += 1
        
        # Обновление точности модели
        if self.bayesian_model.total_predictions > 0:
            self.bayesian_model.model_accuracy = (
                self.bayesian_model.successful_predictions / 
                self.bayesian_model.total_predictions
            )
        
        # Адаптивное обновление приоров
        adaptation = self.bayesian_model.adaptation_rate
        
        if signal_type == "bullish_reversal":
            if actual_outcome:
                self.bayesian_model.prior_bullish += adaptation * (1 - self.bayesian_model.prior_bullish)
            else:
                self.bayesian_model.prior_bullish -= adaptation * self.bayesian_model.prior_bullish
        
        elif signal_type == "bearish_reversal":
            if actual_outcome:
                self.bayesian_model.prior_bearish += adaptation * (1 - self.bayesian_model.prior_bearish)
            else:
                self.bayesian_model.prior_bearish -= adaptation * self.bayesian_model.prior_bearish
        
        # Нормализация приоров
        total = self.bayesian_model.prior_bullish + self.bayesian_model.prior_bearish
        if total > 0:
            self.bayesian_model.prior_bullish /= total
            self.bayesian_model.prior_bearish /= total
        
        self.bayesian_model.training_samples += 1
    
    def calculate_bayesian_probability(self, evidence: Dict[str, Any]) -> Tuple[float, str]:
        """Расчет байесовской вероятности"""
        # Базовые приоры
        prior_bull = self.bayesian_model.prior_bullish
        prior_bear = self.bayesian_model.prior_bearish
        
        # Likelihood на основе технических индикаторов
        likelihood_bull = 0.5
        likelihood_bear = 0.5
        
        # MACD дивергенция
        if evidence.get('macd_divergence') == 'bullish':
            likelihood_bull *= 1.8
        elif evidence.get('macd_divergence') == 'bearish':
            likelihood_bear *= 1.8
        
        # RSI дивергенция
        if evidence.get('rsi_divergence') == 'bullish':
            likelihood_bull *= 1.6
        elif evidence.get('rsi_divergence') == 'bearish':
            likelihood_bear *= 1.6
        
        # Свечные паттерны
        patterns = evidence.get('candle_patterns', [])
        bullish_patterns = ['hammer', 'bullish_engulfing']
        bearish_patterns = ['shooting_star', 'bearish_engulfing']
        
        for pattern in patterns:
            if pattern in bullish_patterns:
                likelihood_bull *= 1.4
            elif pattern in bearish_patterns:
                likelihood_bear *= 1.4
        
        # Объемное подтверждение
        volume_analysis = evidence.get('volume_analysis', {})
        if volume_analysis.get('spike', False):
            volume_strength = volume_analysis.get('strength', 0) / 100
            likelihood_bull *= (1 + volume_strength * 0.5)
            likelihood_bear *= (1 + volume_strength * 0.5)
        
        # Уровни поддержки/сопротивления
        near_support = evidence.get('near_support', False)
        near_resistance = evidence.get('near_resistance', False)
        
        if near_support:
            likelihood_bull *= 1.3
        if near_resistance:
            likelihood_bear *= 1.3
        
        # Расчет posterior probability
        posterior_bull = prior_bull * likelihood_bull
        posterior_bear = prior_bear * likelihood_bear
        
        # Нормализация
        total_posterior = posterior_bull + posterior_bear
        if total_posterior > 0:
            prob_bull = posterior_bull / total_posterior
            prob_bear = posterior_bear / total_posterior
        else:
            prob_bull = prob_bear = 0.5
        
        # Определение направления и вероятности
        if prob_bull > prob_bear:
            return prob_bull * 100, "bullish_reversal"
        else:
            return prob_bear * 100, "bearish_reversal"
    
    def calculate_ai_confidence(self, evidence: Dict[str, Any], probability: float) -> float:
        """Расчет уверенности AI"""
        base_confidence = min(probability, 85.0)  # Базовая уверенность
        
        # Бонусы за качество сигнала
        confidence_bonus = 0
        
        # Множественные подтверждения
        confirmations = 0
        if evidence.get('macd_divergence') != 'none':
            confirmations += 1
        if evidence.get('rsi_divergence') != 'none':
            confirmations += 1
        if evidence.get('candle_patterns'):
            confirmations += 1
        if evidence.get('volume_analysis', {}).get('spike', False):
            confirmations += 1
        if evidence.get('near_support') or evidence.get('near_resistance'):
            confirmations += 1
        
        confidence_bonus += confirmations * 5
        
        # Точность модели
        model_accuracy_bonus = self.bayesian_model.model_accuracy * 20
        
        # Количество обучающих образцов
        training_bonus = min(self.bayesian_model.training_samples / 100 * 10, 10)
        
        final_confidence = base_confidence + confidence_bonus + model_accuracy_bonus + training_bonus
        
        return min(final_confidence, 95.0)  # Максимум 95%
    
    def calculate_mathematical_expectation(self, probability: float, confidence: float) -> float:
        """Расчет математического ожидания"""
        # Базовые параметры
        win_rate = probability / 100
        loss_rate = 1 - win_rate
        
        # Средний выигрыш и проигрыш на основе R:R
        avg_win = self.config['risk_reward_ratio']
        avg_loss = -1.0
        
        # Корректировка на уверенность
        confidence_factor = confidence / 100
        adjusted_win_rate = win_rate * confidence_factor
        adjusted_loss_rate = 1 - adjusted_win_rate
        
        # Математическое ожидание
        expectation = (adjusted_win_rate * avg_win) + (adjusted_loss_rate * avg_loss)
        
        return expectation
    
    def calculate_risk_score(self, evidence: Dict[str, Any], confidence: float) -> float:
        """Расчет оценки риска"""
        base_risk = 50.0  # Базовый риск
        
        # Снижение риска при высокой уверенности
        confidence_reduction = (confidence - 50) * 0.3
        
        # Снижение риска при объемном подтверждении
        volume_reduction = 0
        if evidence.get('volume_analysis', {}).get('spike', False):
            volume_reduction = 10
        
        # Снижение риска при множественных подтверждениях
        confirmations = len([x for x in [
            evidence.get('macd_divergence') != 'none',
            evidence.get('rsi_divergence') != 'none',
            bool(evidence.get('candle_patterns')),
            evidence.get('near_support') or evidence.get('near_resistance')
        ] if x])
        
        confirmation_reduction = confirmations * 5
        
        final_risk = base_risk - confidence_reduction - volume_reduction - confirmation_reduction
        
        return max(5.0, min(final_risk, 95.0))  # Риск от 5% до 95%
    
    def process_bar(self, ohlc: Dict[str, List[float]], volume: List[float], bar_index: int) -> AISignal:
        """Обработка нового бара и генерация AI сигнала"""
        if len(ohlc['close']) < 50:  # Минимум данных для анализа
            return AISignal(
                signal_type="none",
                probability=0.0,
                confidence=0.0,
                strength=0,
                math_expectation=0.0,
                risk_score=50.0,
                reasoning="Недостаточно данных для анализа",
                is_valid=False,
                timestamp=bar_index
            )
        
        # Расчет технических индикаторов
        macd, macd_signal, macd_histogram = self.calculate_macd(ohlc['close'])
        rsi = self.calculate_rsi(ohlc['close'])
        atr = self.calculate_atr(ohlc['high'], ohlc['low'], ohlc['close'])
        
        # Сохранение истории
        self.macd_history.append({
            'macd': macd,
            'signal': macd_signal,
            'histogram': macd_histogram,
            'bar_index': bar_index
        })
        
        self.rsi_history.append({
            'rsi': rsi,
            'bar_index': bar_index
        })
        
        self.atr_history.append({
            'atr': atr,
            'bar_index': bar_index
        })
        
        # Обнаружение дивергенций
        macd_values = [h['macd'] for h in self.macd_history]
        rsi_values = [h['rsi'] for h in self.rsi_history]
        
        macd_divergence = self.detect_macd_divergence(ohlc['close'], macd_values)
        rsi_divergence = self.detect_rsi_divergence(ohlc['close'], rsi_values)
        
        # Обнаружение свечных паттернов
        candle_patterns = self.detect_candle_patterns(ohlc)
        
        # Обновление уровней поддержки/сопротивления
        self.update_support_resistance(ohlc, bar_index)
        
        # Анализ объема
        volume_analysis = self.analyze_volume(volume)
        
        # Проверка близости к уровням S/R
        current_price = ohlc['close'][-1]
        near_support = any(abs(level.price - current_price) / current_price < 0.005 
                          for level in self.support_levels)
        near_resistance = any(abs(level.price - current_price) / current_price < 0.005 
                             for level in self.resistance_levels)
        
        # Сбор evidence для байесовского анализа
        evidence = {
            'macd_divergence': macd_divergence,
            'rsi_divergence': rsi_divergence,
            'candle_patterns': candle_patterns,
            'volume_analysis': volume_analysis,
            'near_support': near_support,
            'near_resistance': near_resistance,
            'rsi': rsi,
            'macd_histogram': macd_histogram
        }
        
        # Расчет байесовской вероятности
        probability, signal_type = self.calculate_bayesian_probability(evidence)
        
        # Расчет уверенности AI
        confidence = self.calculate_ai_confidence(evidence, probability)
        
        # Расчет математического ожидания
        math_expectation = self.calculate_mathematical_expectation(probability, confidence)
        
        # Расчет оценки риска
        risk_score = self.calculate_risk_score(evidence, confidence)
        
        # Расчет силы сигнала
        strength = min(100, int(probability * 0.7 + confidence * 0.3))
        
        # Генерация обоснования
        reasoning = self._generate_reasoning(evidence, probability, confidence)
        
        # Проверка валидности сигнала
        is_valid = (
            probability >= 70.0 and
            confidence >= self.config['ai_confidence_threshold'] and
            strength >= self.config['min_signal_strength'] and
            math_expectation > 0
        )
        
        # Создание AI сигнала
        ai_signal = AISignal(
            signal_type=signal_type,
            probability=probability,
            confidence=confidence,
            strength=strength,
            math_expectation=math_expectation,
            risk_score=risk_score,
            reasoning=reasoning,
            is_valid=is_valid,
            timestamp=bar_index
        )
        
        # Сохранение в историю
        self.ai_signals_history.append(ai_signal)
        
        return ai_signal
    
    def _generate_reasoning(self, evidence: Dict[str, Any], probability: float, confidence: float) -> str:
        """Генерация обоснования сигнала"""
        reasons = []
        
        if evidence['macd_divergence'] != 'none':
            reasons.append(f"MACD дивергенция: {evidence['macd_divergence']}")
        
        if evidence['rsi_divergence'] != 'none':
            reasons.append(f"RSI дивергенция: {evidence['rsi_divergence']}")
        
        if evidence['candle_patterns']:
            patterns_str = ", ".join(evidence['candle_patterns'])
            reasons.append(f"Свечные паттерны: {patterns_str}")
        
        if evidence['volume_analysis']['spike']:
            reasons.append(f"Всплеск объема: {evidence['volume_analysis']['ratio']:.1f}x")
        
        if evidence['near_support']:
            reasons.append("Близость к поддержке")
        
        if evidence['near_resistance']:
            reasons.append("Близость к сопротивлению")
        
        if not reasons:
            reasons.append("Статистический анализ")
        
        return "; ".join(reasons)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Получение статистики производительности"""
        if not self.ai_signals_history:
            return self.performance_stats
        
        valid_signals = [s for s in self.ai_signals_history if s.is_valid]
        
        self.performance_stats.update({
            'total_signals': len(valid_signals),
            'avg_probability': np.mean([s.probability for s in valid_signals]) if valid_signals else 0,
            'avg_confidence': np.mean([s.confidence for s in valid_signals]) if valid_signals else 0,
            'avg_math_expectation': np.mean([s.math_expectation for s in valid_signals]) if valid_signals else 0,
            'model_accuracy': self.bayesian_model.model_accuracy * 100,
            'training_samples': self.bayesian_model.training_samples
        })
        
        return self.performance_stats
    
    def save_state(self, filepath: str):
        """Сохранение состояния индикатора"""
        state = {
            'config': self.config,
            'bayesian_model': {
                'prior_bullish': self.bayesian_model.prior_bullish,
                'prior_bearish': self.bayesian_model.prior_bearish,
                'model_accuracy': self.bayesian_model.model_accuracy,
                'training_samples': self.bayesian_model.training_samples,
                'successful_predictions': self.bayesian_model.successful_predictions,
                'total_predictions': self.bayesian_model.total_predictions
            },
            'performance_stats': self.performance_stats
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
    
    def load_state(self, filepath: str):
        """Загрузка состояния индикатора"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            self.config.update(state.get('config', {}))
            
            bayesian_data = state.get('bayesian_model', {})
            self.bayesian_model.prior_bullish = bayesian_data.get('prior_bullish', 0.5)
            self.bayesian_model.prior_bearish = bayesian_data.get('prior_bearish', 0.5)
            self.bayesian_model.model_accuracy = bayesian_data.get('model_accuracy', 0.5)
            self.bayesian_model.training_samples = bayesian_data.get('training_samples', 0)
            self.bayesian_model.successful_predictions = bayesian_data.get('successful_predictions', 0)
            self.bayesian_model.total_predictions = bayesian_data.get('total_predictions', 0)
            
            self.performance_stats.update(state.get('performance_stats', {}))
            
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Ошибка загрузки состояния: {e}")